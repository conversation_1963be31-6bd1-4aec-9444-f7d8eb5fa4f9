# Load required libraries
library(readxl)
library(igraph)
library(vegan)
library(tidyverse)

# Configuration: Choose taxonomic level for analysis
TAXONOMIC_LEVEL <- "Family"  # Options: "Genus", "Family", "Order", "Class"

# Read data files for the new dataset
# ASV tables
bacteria_asv <- read_excel("ASV_table_bacteria.xlsx")
fungi_asv <- read_excel("ASV_table_fungi.xlsx")

# Taxonomy files
bacteria_tax <- read_excel("taxatable_bacteria.xlsx")
fungi_tax <- read_excel("taxatable_fungi.xlsx")

# Metadata files
bacteria_meta <- read_excel("metadata_bacteria.xlsx")
fungi_meta <- read_excel("metadata_fungi.xlsx")

# Function to aggregate ASVs to specified taxonomic level
aggregate_to_taxonomic_level <- function(asv_table, taxonomy_table, taxonomic_level) {
  cat("\n--- Aggregating to", taxonomic_level, "level ---\n")
  
  # Check if taxonomic level exists in taxonomy table
  if (!taxonomic_level %in% colnames(taxonomy_table)) {
    stop("Taxonomic level '", taxonomic_level, "' not found in taxonomy table")
  }
  
  # Get the first column name (ASV ID column)
  asv_id_col <- colnames(asv_table)[1]
  tax_asv_id_col <- colnames(taxonomy_table)[1]
  
  # Merge ASV table with taxonomy
  merged_data <- merge(asv_table, taxonomy_table[, c(tax_asv_id_col, taxonomic_level)], 
                     by.x = asv_id_col, by.y = tax_asv_id_col, all.x = TRUE)
  
  # Remove rows with NA or empty taxonomic assignment
  valid_rows <- !is.na(merged_data[[taxonomic_level]]) & 
              merged_data[[taxonomic_level]] != "" & 
              merged_data[[taxonomic_level]] != "NA"
  
  if (sum(valid_rows) == 0) {
    stop("No valid taxonomic assignments found at ", taxonomic_level, " level")
  }
  
  cat("Removed", sum(!valid_rows), "ASVs with missing", taxonomic_level, "assignment\n")
  merged_data <- merged_data[valid_rows, ]
  
  # Get sample columns (all columns except ASV ID and taxonomic level)
  sample_cols <- setdiff(colnames(merged_data), c(asv_id_col, taxonomic_level))
  
  # Aggregate by summing ASV counts within each taxonomic group
  aggregated <- merged_data %>%
    group_by(!!sym(taxonomic_level)) %>%
    summarise(across(all_of(sample_cols), sum, na.rm = TRUE), .groups = 'drop')
  
  # Rename the taxonomic level column to match original ASV ID column name
  colnames(aggregated)[1] <- asv_id_col
  
  cat("Aggregated from", nrow(asv_table), "ASVs to", nrow(aggregated), taxonomic_level, "groups\n")
  
  return(as.data.frame(aggregated))
}

# Function to filter samples by substrate type
filter_samples_by_substrate <- function(asv_table, metadata, substrate_type) {
  # Determine the sample ID column name
  sample_id_col <- colnames(metadata)[1]  # First column is Sample
  
  # Get sample IDs for the specified substrate
  target_samples <- metadata[[sample_id_col]][metadata$Substrate == substrate_type]
  
  cat("Found", length(target_samples), "target samples for substrate", substrate_type, "\n")
  
  # Find which columns in ASV table correspond to these samples
  sample_cols <- which(colnames(asv_table) %in% target_samples)
  
  if (length(sample_cols) == 0) {
    cat("Warning: No samples found for substrate", substrate_type, "\n")
    return(NULL)
  }
  
  # Return ASV table with only the target samples (plus the first column with IDs)
  filtered_table <- asv_table[, c(1, sample_cols)]
  cat("Filtered", substrate_type, "samples:", ncol(filtered_table) - 1, "samples\n")
  return(filtered_table)
}

# Function to filter samples by region
filter_samples_by_region <- function(asv_table, metadata, region_type) {
  # Determine the sample ID column name
  sample_id_col <- colnames(metadata)[1]  # First column is Sample
  
  # Get sample IDs for the specified region
  target_samples <- metadata[[sample_id_col]][metadata$Region == region_type]
  
  cat("Found", length(target_samples), "target samples for region", region_type, "\n")
  
  # Find which columns in ASV table correspond to these samples
  sample_cols <- which(colnames(asv_table) %in% target_samples)
  
  if (length(sample_cols) == 0) {
    cat("Warning: No samples found for region", region_type, "\n")
    return(NULL)
  }
  
  # Return ASV table with only the target samples (plus the first column with IDs)
  filtered_table <- asv_table[, c(1, sample_cols)]
  cat("Filtered", region_type, "samples:", ncol(filtered_table) - 1, "samples\n")
  return(filtered_table)
}

# Aggregate all datasets to the specified taxonomic level
bacteria_agg <- aggregate_to_taxonomic_level(bacteria_asv, bacteria_tax, TAXONOMIC_LEVEL)
fungi_agg <- aggregate_to_taxonomic_level(fungi_asv, fungi_tax, TAXONOMIC_LEVEL)

# Create filtered datasets for different substrates
# Get unique substrate values
substrate_types <- unique(bacteria_meta$Substrate)
cat("Found substrate types:", substrate_types, "\n")

# Bacteria by substrate
bacteria_by_substrate <- list()
for (substrate in substrate_types) {
  bacteria_by_substrate[[substrate]] <- filter_samples_by_substrate(bacteria_agg, bacteria_meta, substrate)
}

# Fungi by substrate
fungi_by_substrate <- list()
for (substrate in substrate_types) {
  fungi_by_substrate[[substrate]] <- filter_samples_by_substrate(fungi_agg, fungi_meta, substrate)
}

# Create filtered datasets for different regions
# Get unique region values
region_types <- unique(bacteria_meta$Region)
cat("Found region types:", region_types, "\n")

# Bacteria by region
bacteria_by_region <- list()
for (region in region_types) {
  bacteria_by_region[[region]] <- filter_samples_by_region(bacteria_agg, bacteria_meta, region)
}

# Fungi by region
fungi_by_region <- list()
for (region in region_types) {
  fungi_by_region[[region]] <- filter_samples_by_region(fungi_agg, fungi_meta, region)
}

cat("\n========== SUMMARY OF FILTERED DATASETS ==========\n")

# Print dimensions of substrate-filtered datasets
for (substrate in substrate_types) {
  if (!is.null(bacteria_by_substrate[[substrate]])) {
    cat("Bacteria", substrate, ":", dim(bacteria_by_substrate[[substrate]]), "\n")
  }
  if (!is.null(fungi_by_substrate[[substrate]])) {
    cat("Fungi", substrate, ":", dim(fungi_by_substrate[[substrate]]), "\n")
  }
}

# Print dimensions of region-filtered datasets
for (region in region_types) {
  if (!is.null(bacteria_by_region[[region]])) {
    cat("Bacteria", region, ":", dim(bacteria_by_region[[region]]), "\n")
  }
  if (!is.null(fungi_by_region[[region]])) {
    cat("Fungi", region, ":", dim(fungi_by_region[[region]]), "\n")
  }
}

# Function to create correlation matrix for taxonomic-level data
create_taxonomic_correlation_matrix <- function(taxonomic_table, threshold = 0.6) {
  cat("\n--- Creating correlation matrix ---\n")
  cat("Input dimensions:", dim(taxonomic_table), "\n")

  # Check if the table has at least one column besides the taxonomic IDs
  if (ncol(taxonomic_table) <= 1) {
    stop("Table must have at least one sample column besides the taxonomic IDs")
  }

  tryCatch({
    # Extract abundance matrix (remove first column which contains taxonomic names)
    abundance_matrix <- as.matrix(taxonomic_table[,-1])
    rownames(abundance_matrix) <- taxonomic_table[[1]]

    # Check for rows with all zeros or constant values
    row_sums <- rowSums(abundance_matrix)
    constant_rows <- row_sums == 0 | apply(abundance_matrix, 1, function(x) length(unique(x)) <= 1)

    if (any(constant_rows)) {
      cat("Removing", sum(constant_rows), "constant rows (all zeros or all same value)\n")
      abundance_matrix <- abundance_matrix[!constant_rows, , drop = FALSE]
    }

    # Check if we still have enough rows
    if (nrow(abundance_matrix) <= 1) {
      stop("Not enough variable rows for correlation analysis after removing constant rows")
    }

    cat("Final matrix for correlation:", nrow(abundance_matrix), "taxa ×", ncol(abundance_matrix), "samples\n")

    # Calculate correlations
    cat("Calculating Spearman correlations...\n")
    cor_matrix <- cor(t(abundance_matrix), method = "spearman", use = "pairwise.complete.obs")

    # Replace any NAs with 0
    if (any(is.na(cor_matrix))) {
      cat("Replacing", sum(is.na(cor_matrix)), "NA values with 0\n")
      cor_matrix[is.na(cor_matrix)] <- 0
    }

    # Print summary before thresholding
    cat("\nBefore thresholding:\n")
    cat("Total correlations:", length(cor_matrix), "\n")
    cat("Positive correlations:", sum(cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cor_matrix < 0), "\n")
    cat("Range:", range(cor_matrix), "\n")

    # Store signs before thresholding
    signs <- sign(cor_matrix)

    # Apply threshold to absolute values
    cor_matrix[abs(cor_matrix) < threshold] <- 0

    # Restore signs
    cor_matrix <- cor_matrix * signs

    # Set diagonal to zero
    diag(cor_matrix) <- 0

    # Print summary after thresholding
    cat("\nAfter thresholding (|correlation| ≥", threshold, "):\n")
    cat("Total non-zero correlations:", sum(cor_matrix != 0), "\n")
    cat("Positive correlations:", sum(cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cor_matrix < 0), "\n")

    if (sum(cor_matrix != 0) > 0) {
      cat("Range of non-zero correlations:", range(cor_matrix[cor_matrix != 0]), "\n")
    } else {
      cat("No correlations above threshold.\n")
    }

    return(list(
      cor_matrix = cor_matrix,
      taxa_names = rownames(abundance_matrix),
      n_taxa = nrow(abundance_matrix)
    ))

  }, error = function(e) {
    cat("Error in correlation calculation:", e$message, "\n")
    return(NULL)
  })
}

# Function to analyze taxonomic network
analyze_taxonomic_network <- function(cor_result, group_name) {
  if (is.null(cor_result)) {
    cat("Cannot analyze network: correlation result is NULL\n")
    return(NULL)
  }

  cat("\n========== ANALYZING", group_name, "NETWORK ==========\n")

  cor_matrix <- cor_result$cor_matrix
  taxa_names <- cor_result$taxa_names

  # Check if we have any correlations
  if (sum(cor_matrix != 0) == 0) {
    cat("No correlations above threshold. Cannot create network.\n")
    return(NULL)
  }

  tryCatch({
    # Create igraph network from correlation matrix
    network <- graph_from_adjacency_matrix(
      abs(cor_matrix),
      mode = "undirected",
      weighted = TRUE,
      diag = FALSE
    )

    # Add taxa names as vertex names
    V(network)$name <- taxa_names

    # Get edge list to properly assign edge attributes
    edge_list <- as_edgelist(network, names = FALSE)

    # Extract correlation values for each edge
    edge_correlations <- numeric(nrow(edge_list))
    edge_signs <- character(nrow(edge_list))

    for (i in 1:nrow(edge_list)) {
      row_idx <- edge_list[i, 1]
      col_idx <- edge_list[i, 2]
      correlation_value <- cor_matrix[row_idx, col_idx]
      edge_correlations[i] <- correlation_value
      edge_signs[i] <- ifelse(correlation_value > 0, "positive", "negative")
    }

    # Add edge attributes
    E(network)$sign <- edge_signs
    E(network)$correlation <- edge_correlations

    # Calculate network metrics
    cat("Network created with", vcount(network), "nodes and", ecount(network), "edges\n")

    # Node degree
    node_degrees <- degree(network)
    cat("Average degree:", mean(node_degrees), "\n")
    cat("Max degree:", max(node_degrees), "\n")

    # Centrality measures
    betweenness <- betweenness(network)
    closeness <- closeness(network)

    # Community detection
    communities <- cluster_louvain(network)
    community_count <- length(communities)
    modularity_score <- modularity(communities)

    cat("Communities detected:", community_count, "\n")
    cat("Modularity:", modularity_score, "\n")

    # Identify hub nodes (top 5% by degree for more selectivity)
    degree_threshold <- quantile(node_degrees, 0.95)
    hub_nodes <- names(node_degrees[node_degrees >= degree_threshold])
    cat("Hub nodes (top 5% by degree):", length(hub_nodes), "\n")

    # Create results list
    results <- list(
      network = network,
      node_degrees = node_degrees,
      betweenness = betweenness,
      closeness = closeness,
      communities = communities,
      community_count = community_count,
      modularity = modularity_score,
      hub_nodes = hub_nodes,
      taxa_names = taxa_names,
      group_name = group_name
    )

    return(results)

  }, error = function(e) {
    cat("Error in network analysis:", e$message, "\n")
    return(NULL)
  })
}

# Function to create cross-correlation matrix between two taxonomic tables
create_taxonomic_cross_correlation_matrix <- function(table1, table2, threshold = 0.6) {
  cat("\n--- Creating cross-correlation matrix ---\n")
  cat("Table 1 dimensions:", dim(table1), "\n")
  cat("Table 2 dimensions:", dim(table2), "\n")

  tryCatch({
    # Extract abundance matrices (remove first column which contains taxonomic names)
    abundance_matrix1 <- as.matrix(table1[,-1])
    abundance_matrix2 <- as.matrix(table2[,-1])

    rownames(abundance_matrix1) <- table1[[1]]
    rownames(abundance_matrix2) <- table2[[1]]

    # Remove constant rows from both matrices
    row_sums1 <- rowSums(abundance_matrix1)
    constant_rows1 <- row_sums1 == 0 | apply(abundance_matrix1, 1, function(x) length(unique(x)) <= 1)
    if (any(constant_rows1)) {
      cat("Removing", sum(constant_rows1), "constant rows from table 1\n")
      abundance_matrix1 <- abundance_matrix1[!constant_rows1, , drop = FALSE]
    }

    row_sums2 <- rowSums(abundance_matrix2)
    constant_rows2 <- row_sums2 == 0 | apply(abundance_matrix2, 1, function(x) length(unique(x)) <= 1)
    if (any(constant_rows2)) {
      cat("Removing", sum(constant_rows2), "constant rows from table 2\n")
      abundance_matrix2 <- abundance_matrix2[!constant_rows2, , drop = FALSE]
    }

    # Check if we have enough data
    if (nrow(abundance_matrix1) <= 1 || nrow(abundance_matrix2) <= 1) {
      stop("Not enough variable rows for cross-correlation analysis")
    }

    # Get sample names and find common samples
    samples1 <- colnames(abundance_matrix1)
    samples2 <- colnames(abundance_matrix2)
    common_samples <- intersect(samples1, samples2)

    if (length(common_samples) == 0) {
      stop("No common samples found between the two tables")
    }

    cat("Found", length(common_samples), "common samples\n")

    # Subset to common samples
    abundance_matrix1 <- abundance_matrix1[, common_samples, drop = FALSE]
    abundance_matrix2 <- abundance_matrix2[, common_samples, drop = FALSE]

    cat("Final matrices: ", nrow(abundance_matrix1), "×", ncol(abundance_matrix1),
        " vs ", nrow(abundance_matrix2), "×", ncol(abundance_matrix2), "\n")

    # Calculate cross-correlations
    cat("Calculating cross-correlations...\n")
    cross_cor_matrix <- cor(t(abundance_matrix1), t(abundance_matrix2),
                           method = "spearman", use = "pairwise.complete.obs")

    # Replace any NAs with 0
    if (any(is.na(cross_cor_matrix))) {
      cat("Replacing", sum(is.na(cross_cor_matrix)), "NA values with 0\n")
      cross_cor_matrix[is.na(cross_cor_matrix)] <- 0
    }

    # Print summary before thresholding
    cat("\nBefore thresholding:\n")
    cat("Total correlations:", length(cross_cor_matrix), "\n")
    cat("Positive correlations:", sum(cross_cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cross_cor_matrix < 0), "\n")
    cat("Range:", range(cross_cor_matrix), "\n")

    # Apply threshold and keep only positive correlations (like individual networks)
    cross_cor_matrix[abs(cross_cor_matrix) < threshold] <- 0
    cross_cor_matrix[cross_cor_matrix < 0] <- 0  # Remove negative correlations

    # Print summary after thresholding
    cat("\nAfter thresholding (correlation ≥", threshold, ") and keeping only positive:\n")
    cat("Total positive correlations:", sum(cross_cor_matrix > 0), "\n")
    cat("Negative correlations removed for consistency with individual networks\n")

    if (sum(cross_cor_matrix > 0) > 0) {
      cat("Range of positive correlations:", range(cross_cor_matrix[cross_cor_matrix > 0]), "\n")
    } else {
      cat("No positive correlations above threshold.\n")
    }

    return(list(
      cor_matrix = cross_cor_matrix,
      taxa_names1 = rownames(abundance_matrix1),
      taxa_names2 = rownames(abundance_matrix2),
      common_samples = common_samples
    ))

  }, error = function(e) {
    cat("Error in cross-correlation calculation:", e$message, "\n")
    return(NULL)
  })
}

# Function to analyze cross-correlation network
analyze_taxonomic_cross_network <- function(cross_cor_result, group1_name, group2_name) {
  if (is.null(cross_cor_result)) {
    cat("Cannot analyze cross-network: correlation result is NULL\n")
    return(NULL)
  }

  cat("\n========== ANALYZING", group1_name, "vs", group2_name, "CROSS-NETWORK ==========\n")

  cross_cor_matrix <- cross_cor_result$cor_matrix
  taxa_names1 <- cross_cor_result$taxa_names1
  taxa_names2 <- cross_cor_result$taxa_names2

  # Check if we have any positive correlations
  if (sum(cross_cor_matrix > 0) == 0) {
    cat("No positive correlations above threshold. Cannot create cross-network.\n")
    return(NULL)
  }

  tryCatch({
    # Create bipartite network with only positive correlations
    # First, create an edge list from the correlation matrix (only positive values)
    edges <- which(cross_cor_matrix > 0, arr.ind = TRUE)
    edge_list <- data.frame(
      from = taxa_names1[edges[,1]],
      to = taxa_names2[edges[,2]],
      weight = cross_cor_matrix[edges],  # All positive, so no need for abs()
      correlation = cross_cor_matrix[edges],
      sign = "positive",  # All edges are positive now
      stringsAsFactors = FALSE
    )

    # Create igraph network
    network <- graph_from_data_frame(edge_list, directed = FALSE)

    # Debug information
    cat("Created network with", vcount(network), "vertices and", ecount(network), "edges\n")
    cat("Vertex names sample:", head(V(network)$name, 10), "\n")

    # Add vertex attributes to distinguish between the two groups
    V(network)$type <- ifelse(V(network)$name %in% taxa_names1, group1_name, group2_name)

    # Verify vertex types
    cat("Group assignments - ", group1_name, ":", sum(V(network)$type == group1_name),
        ", ", group2_name, ":", sum(V(network)$type == group2_name), "\n")

    # Calculate network metrics
    cat("Cross-network created with", vcount(network), "nodes and", ecount(network), "positive edges\n")
    cat("Group 1 (", group1_name, ") nodes:", sum(V(network)$type == group1_name), "\n")
    cat("Group 2 (", group2_name, ") nodes:", sum(V(network)$type == group2_name), "\n")
    cat("Note: Only positive correlations included for consistency with individual networks\n")

    # Node degree
    node_degrees <- degree(network)
    cat("Average degree:", mean(node_degrees), "\n")
    cat("Max degree:", max(node_degrees), "\n")

    # Centrality measures
    betweenness <- betweenness(network)
    closeness <- closeness(network)

    # Community detection
    communities <- cluster_louvain(network)
    community_count <- length(communities)
    modularity_score <- modularity(communities)

    cat("Communities detected:", community_count, "\n")
    cat("Modularity:", modularity_score, "\n")

    # Identify hub nodes (top 5% by degree for more selectivity)
    degree_threshold <- quantile(node_degrees, 0.95)
    hub_nodes <- names(node_degrees[node_degrees >= degree_threshold])
    cat("Hub nodes (top 5% by degree):", length(hub_nodes), "\n")

    # Create results list
    results <- list(
      network = network,
      node_degrees = node_degrees,
      betweenness = betweenness,
      closeness = closeness,
      communities = communities,
      community_count = community_count,
      modularity = modularity_score,
      hub_nodes = hub_nodes,
      edge_list = edge_list,
      group1_name = group1_name,
      group2_name = group2_name
    )

    return(results)

  }, error = function(e) {
    cat("Error in cross-network analysis:", e$message, "\n")
    return(NULL)
  })
}

# Perform individual network analyses for each dataset
# Initialize storage for network results
individual_networks <- list()

# 1. Bacteria networks by substrate
cat("\n--- BACTERIA NETWORKS BY SUBSTRATE ---\n")
for (substrate in substrate_types) {
  if (!is.null(bacteria_by_substrate[[substrate]])) {
    group_name <- paste("Bacteria", substrate, sep="_")
    cat("\nAnalyzing", group_name, "\n")

    cor_result <- create_taxonomic_correlation_matrix(bacteria_by_substrate[[substrate]], threshold = 0.6)
    network_result <- analyze_taxonomic_network(cor_result, group_name)

    if (!is.null(network_result)) {
      individual_networks[[group_name]] <- network_result
    }
  }
}

# 2. Fungi networks by substrate
cat("\n--- FUNGI NETWORKS BY SUBSTRATE ---\n")
for (substrate in substrate_types) {
  if (!is.null(fungi_by_substrate[[substrate]])) {
    group_name <- paste("Fungi", substrate, sep="_")
    cat("\nAnalyzing", group_name, "\n")

    cor_result <- create_taxonomic_correlation_matrix(fungi_by_substrate[[substrate]], threshold = 0.6)
    network_result <- analyze_taxonomic_network(cor_result, group_name)

    if (!is.null(network_result)) {
      individual_networks[[group_name]] <- network_result
    }
  }
}

# 3. Bacteria networks by region
cat("\n--- BACTERIA NETWORKS BY REGION ---\n")
for (region in region_types) {
  if (!is.null(bacteria_by_region[[region]])) {
    group_name <- paste("Bacteria", gsub(" ", "_", region), sep="_")
    cat("\nAnalyzing", group_name, "\n")

    cor_result <- create_taxonomic_correlation_matrix(bacteria_by_region[[region]], threshold = 0.6)
    network_result <- analyze_taxonomic_network(cor_result, group_name)

    if (!is.null(network_result)) {
      individual_networks[[group_name]] <- network_result
    }
  }
}

# 4. Fungi networks by region
cat("\n--- FUNGI NETWORKS BY REGION ---\n")
for (region in region_types) {
  if (!is.null(fungi_by_region[[region]])) {
    group_name <- paste("Fungi", gsub(" ", "_", region), sep="_")
    cat("\nAnalyzing", group_name, "\n")

    cor_result <- create_taxonomic_correlation_matrix(fungi_by_region[[region]], threshold = 0.6)
    network_result <- analyze_taxonomic_network(cor_result, group_name)

    if (!is.null(network_result)) {
      individual_networks[[group_name]] <- network_result
    }
  }
}

# 5. Overall bacteria and fungi networks (full dataset)
# Overall bacteria network
cat("\nAnalyzing overall bacteria network\n")
bacteria_overall_cor <- create_taxonomic_correlation_matrix(bacteria_agg, threshold = 0.6)
bacteria_overall_network <- analyze_taxonomic_network(bacteria_overall_cor, "Bacteria_Overall")
if (!is.null(bacteria_overall_network)) {
  individual_networks[["Bacteria_Overall"]] <- bacteria_overall_network
}

# Overall fungi network
cat("\nAnalyzing overall fungi network\n")
fungi_overall_cor <- create_taxonomic_correlation_matrix(fungi_agg, threshold = 0.6)
fungi_overall_network <- analyze_taxonomic_network(fungi_overall_cor, "Fungi_Overall")
if (!is.null(fungi_overall_network)) {
  individual_networks[["Fungi_Overall"]] <- fungi_overall_network
}

# Helper function to perform cross-correlation with multiple thresholds
perform_taxonomic_cross_correlation <- function(table1, table2, group1_name, group2_name) {
  cat("\n\n----- ", group1_name, " vs ", group2_name, " -----\n", sep="")

  # For cross-correlations, start with lower threshold (0.4) since cross-kingdom correlations are typically weaker
  tryCatch({
    cross_cor <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.4)

    if (!is.null(cross_cor) && sum(cross_cor$cor_matrix > 0) > 0) {
      cat("\nFound positive correlations above threshold 0.4\n")
      cross_network <- analyze_taxonomic_cross_network(cross_cor, group1_name, group2_name)
      return(cross_network)
    } else {
      cat("\nNo positive correlations above threshold 0.4. Trying threshold 0.3...\n")

      # Try with threshold 0.3
      cross_cor_03 <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.3)

      if (!is.null(cross_cor_03) && sum(cross_cor_03$cor_matrix > 0) > 0) {
        cat("\nFound positive correlations above threshold 0.3\n")
        cross_network <- analyze_taxonomic_cross_network(cross_cor_03,
                                                        paste(group1_name, "(0.3)"),
                                                        paste(group2_name, "(0.3)"))
        return(cross_network)
      } else {
        cat("\nNo positive correlations above threshold 0.3. Trying threshold 0.25...\n")

        # Try with threshold 0.25
        cross_cor_025 <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.25)

        if (!is.null(cross_cor_025) && sum(cross_cor_025$cor_matrix > 0) > 0) {
          cat("\nFound positive correlations above threshold 0.25\n")
          cross_network <- analyze_taxonomic_cross_network(cross_cor_025,
                                                          paste(group1_name, "(0.25)"),
                                                          paste(group2_name, "(0.25)"))
          return(cross_network)
        } else {
          cat("\nNo significant positive cross-correlations found even at threshold 0.25\n")
          cat("This may indicate weak positive bacteria-fungi interactions in this environment\n")
          return(NULL)
        }
      }
    }
  }, error = function(e) {
    cat("Error in cross-correlation analysis:", e$message, "\n")
    return(NULL)
  })
}

# Perform cross-correlation analyses
# Initialize storage for cross-network results
cross_networks <- list()

# 1. Cross-correlations by substrate
cat("\n--- CROSS-CORRELATIONS BY SUBSTRATE ---\n")
for (substrate in substrate_types) {
  if (!is.null(bacteria_by_substrate[[substrate]]) && !is.null(fungi_by_substrate[[substrate]])) {
    group1_name <- paste("Bacteria", substrate, sep="_")
    group2_name <- paste("Fungi", substrate, sep="_")

    cross_network <- perform_taxonomic_cross_correlation(
      bacteria_by_substrate[[substrate]],
      fungi_by_substrate[[substrate]],
      group1_name,
      group2_name
    )

    if (!is.null(cross_network)) {
      cross_networks[[paste(group1_name, "vs", group2_name, sep="_")]] <- cross_network
    }
  }
}

# 2. Cross-correlations by region
cat("\n--- CROSS-CORRELATIONS BY REGION ---\n")
for (region in region_types) {
  if (!is.null(bacteria_by_region[[region]]) && !is.null(fungi_by_region[[region]])) {
    group1_name <- paste("Bacteria", gsub(" ", "_", region), sep="_")
    group2_name <- paste("Fungi", gsub(" ", "_", region), sep="_")

    cross_network <- perform_taxonomic_cross_correlation(
      bacteria_by_region[[region]],
      fungi_by_region[[region]],
      group1_name,
      group2_name
    )

    if (!is.null(cross_network)) {
      cross_networks[[paste(group1_name, "vs", group2_name, sep="_")]] <- cross_network
    }
  }
}

# 3. Overall cross-correlation (full dataset)
cat("\n--- OVERALL CROSS-CORRELATION (FULL DATASET) ---\n")
overall_cross_network <- perform_taxonomic_cross_correlation(
  bacteria_agg,
  fungi_agg,
  "Bacteria_Overall",
  "Fungi_Overall"
)

if (!is.null(overall_cross_network)) {
  cross_networks[["Bacteria_Overall_vs_Fungi_Overall"]] <- overall_cross_network
}

cat("\n\n========== NETWORK ANALYSIS SUMMARY ==========\n")
cat("Individual networks created:", length(individual_networks), "\n")
cat("Cross-correlation networks created:", length(cross_networks), "\n")

if (length(individual_networks) > 0) {
  cat("\nIndividual networks:\n")
  for (name in names(individual_networks)) {
    cat("- ", name, ": ", vcount(individual_networks[[name]]$network), " nodes, ",
        ecount(individual_networks[[name]]$network), " edges\n", sep="")
  }
}

if (length(cross_networks) > 0) {
  cat("\nCross-correlation networks:\n")
  for (name in names(cross_networks)) {
    cat("- ", name, ": ", vcount(cross_networks[[name]]$network), " nodes, ",
        ecount(cross_networks[[name]]$network), " edges\n", sep="")
  }
}

cat("\nNetwork analysis completed successfully!\n")

# Function to plot individual networks with improved visualization
plot_individual_network <- function(network_result, group_name, taxonomic_level) {
  if (is.null(network_result)) {
    cat("Cannot plot network: network result is NULL\n")
    return(NULL)
  }

  tryCatch({
    network <- network_result$network

    # Set up layout with better spacing
    layout <- layout_with_fr(network, niter = 1000)

    # Color nodes by community with muted, transparent colors
    if (network_result$community_count <= 12) {
      # Use a predefined set of muted colors for small numbers of communities
      muted_colors <- c("#8DD3C7", "#FFFFB3", "#BEBADA", "#FB8072", "#80B1D3", "#FDB462",
                       "#B3DE69", "#FCCDE5", "#D9D9D9", "#BC80BD", "#CCEBC5", "#FFED6F")
      community_colors <- muted_colors[1:network_result$community_count]
    } else {
      # For larger numbers, use a muted color palette
      community_colors <- rainbow(network_result$community_count, s = 0.6, v = 0.8)
    }
    # Add transparency to node colors (80% opacity)
    node_colors <- adjustcolor(community_colors[membership(network_result$communities)], alpha.f = 0.8)

    # Scale down node sizes significantly and make them more proportional
    max_degree <- max(network_result$node_degrees)
    min_degree <- min(network_result$node_degrees)
    # Scale nodes between 2 and 8 (much smaller than before)
    node_sizes <- 2 + (network_result$node_degrees - min_degree) / (max_degree - min_degree) * 6

    # Improve edge visualization - solid colors with better contrast
    base_edge_colors <- ifelse(E(network)$sign == "positive", "#2E8B57", "#DC143C")  # Sea green and crimson
    edge_colors <- base_edge_colors  # No transparency for better visibility

    # Scale edge widths better
    edge_widths <- 0.5 + abs(E(network)$correlation) * 2

    # More selective hub node identification (top 5% instead of 10%)
    hub_threshold <- quantile(network_result$node_degrees, 0.95)
    is_hub <- network_result$node_degrees >= hub_threshold

    # Create filename
    filename <- paste0(group_name, "_", taxonomic_level, "_network.pdf")

    # Create plot with higher resolution
    pdf(filename, width = 14, height = 12)

    # Create layout for main plot and subplot
    layout(matrix(c(1, 1, 1, 2), nrow = 2, ncol = 2, byrow = TRUE), heights = c(3, 1))

    # Main network plot (no labels for cleaner visualization)
    plot(network,
         layout = layout,
         vertex.color = node_colors,
         vertex.size = node_sizes,
         vertex.frame.color = "white",
         vertex.frame.width = 1.0,
         vertex.label = NA,  # Remove all labels for cleaner plot
         edge.color = edge_colors,
         edge.width = edge_widths,
         edge.curved = 0.1,
         main = paste(group_name, "Network at", taxonomic_level, "Level"),
         sub = paste("Nodes:", vcount(network), "| Edges:", ecount(network),
                    "| Communities:", network_result$community_count,
                    "| Hub nodes (top 5%):", sum(is_hub)),
         cex.main = 1.2,
         cex.sub = 0.9)

    # Create subplot showing hub families with degree (bars) and betweenness (lines)
    par(mar = c(4, 4, 2, 4))  # Extra margin for second y-axis

    if (sum(is_hub) > 0) {
      hub_families <- V(network)$name[is_hub]
      hub_communities <- membership(network_result$communities)[is_hub]
      hub_degrees <- network_result$node_degrees[is_hub]
      hub_betweenness <- network_result$betweenness[is_hub]

      # Create a summary table
      hub_data <- data.frame(
        Family = hub_families,
        Community = hub_communities,
        Degree = hub_degrees,
        Betweenness = hub_betweenness,
        stringsAsFactors = FALSE
      )

      # Sort by degree (descending)
      hub_data <- hub_data[order(hub_data$Degree, decreasing = TRUE), ]

      # Limit families to avoid overcrowding
      if (nrow(hub_data) <= 12) {
        families_to_show <- hub_data
      } else {
        families_to_show <- hub_data[1:12, ]
      }

      # Create color mapping for communities
      community_colors_hub <- community_colors[families_to_show$Community]

      # Create bar plot for degree
      bp <- barplot(families_to_show$Degree,
                    names.arg = families_to_show$Family,
                    col = adjustcolor(community_colors_hub, alpha.f = 0.8),
                    las = 2,
                    cex.names = 0.6,
                    main = "Hub Families: Degree (bars) & Betweenness (line)",
                    ylab = "Degree",
                    cex.main = 0.9,
                    ylim = c(0, max(families_to_show$Degree) * 1.1))

      # Add betweenness as a line plot on secondary y-axis
      par(new = TRUE)

      # Scale betweenness to fit nicely with degree scale
      betweenness_scaled <- families_to_show$Betweenness
      if (max(betweenness_scaled) > 0) {
        betweenness_scaled <- (betweenness_scaled / max(betweenness_scaled)) * max(families_to_show$Degree) * 0.8
      }

      plot(bp, betweenness_scaled,
           type = "o",
           pch = 16,
           col = "red",
           lwd = 2,
           cex = 1.2,
           axes = FALSE,
           xlab = "",
           ylab = "")

      # Add right y-axis for betweenness
      axis(4,
           at = seq(0, max(families_to_show$Degree) * 0.8, length.out = 5),
           labels = round(seq(0, max(families_to_show$Betweenness), length.out = 5), 1),
           col = "red",
           col.axis = "red")
      mtext("Betweenness", side = 4, line = 2.5, col = "red", cex = 0.8)

      # Add legend
      legend("topright",
             legend = c("Degree (bars)", "Betweenness (line)"),
             fill = c("gray", NA),
             border = c("black", NA),
             lty = c(NA, 1),
             pch = c(NA, 16),
             col = c("black", "red"),
             cex = 0.7,
             bg = "white")

    } else {
      # If no hub nodes, show a message
      plot.new()
      text(0.5, 0.5, "No hub nodes identified\n(top 5% threshold)",
           cex = 1.2, adj = 0.5)
    }

    # Add improved legend with better positioning
    legend("bottomright",
           legend = c("Positive correlation", "Negative correlation", "Hub nodes (top 5%)", "Communities"),
           col = c("#2E8B57", "#DC143C", "black", "gray"),
           lty = c(1, 1, NA, NA),
           pch = c(NA, NA, 16, 15),
           cex = 0.9,
           bg = "white",
           box.lty = 1)

    # Reset layout
    layout(1)

    dev.off()

    cat("Network plot saved:", filename, "\n")
    cat("Hub nodes identified:", sum(is_hub), "out of", vcount(network), "total nodes\n")

  }, error = function(e) {
    cat("Error creating network plot:", e$message, "\n")
  })
}

# Function to plot cross-correlation networks with improved visualization
plot_cross_network <- function(cross_network_result, group1_name, group2_name, taxonomic_level) {
  if (is.null(cross_network_result)) {
    cat("Cannot plot cross-network: network result is NULL\n")
    return(NULL)
  }

  tryCatch({
    network <- cross_network_result$network

    # Check if network has vertices
    if (vcount(network) == 0) {
      cat("Network has no vertices, cannot plot\n")
      return(NULL)
    }

    # Try different layouts for better visualization
    # First try bipartite layout, if it fails use force-directed
    layout <- tryCatch({
      layout_as_bipartite(network)
    }, error = function(e) {
      cat("Bipartite layout failed, using force-directed layout\n")
      layout_with_fr(network, niter = 1000)
    })

    # If layout is still problematic, use a simple layout
    if (is.null(layout) || any(is.na(layout))) {
      cat("Using circle layout as fallback\n")
      layout <- layout_in_circle(network)
    }

    # Color nodes by group with muted, transparent colors
    base_group1_color <- "#87CEEB"  # Muted sky blue for bacteria
    base_group2_color <- "#DDA0DD"  # Muted plum/lavender for fungi (distinct from green edges)
    base_node_colors <- ifelse(V(network)$type == cross_network_result$group1_name, base_group1_color, base_group2_color)
    node_colors <- adjustcolor(base_node_colors, alpha.f = 0.8)  # 80% opacity

    # Scale down node sizes significantly
    max_degree <- max(cross_network_result$node_degrees)
    min_degree <- min(cross_network_result$node_degrees)
    # Scale nodes between 3 and 10 (smaller than before)
    node_sizes <- 3 + (cross_network_result$node_degrees - min_degree) / (max_degree - min_degree) * 7

    # Edge visualization - only positive correlations (green)
    edge_colors <- "#228B22"  # Forest green for all positive correlations

    # Scale edge widths (all correlations are positive now)
    edge_widths <- 0.8 + E(network)$correlation * 2.5

    # More selective hub node identification (top 5%)
    hub_threshold <- quantile(cross_network_result$node_degrees, 0.95)
    is_hub <- cross_network_result$node_degrees >= hub_threshold

    # Create filename
    filename <- paste0(group1_name, "_vs_", group2_name, "_", taxonomic_level, "_cross_network.pdf")

    # Create plot with higher resolution
    pdf(filename, width = 16, height = 12)

    # Create layout for main plot and subplot
    layout(matrix(c(1, 1, 1, 2), nrow = 2, ncol = 2, byrow = TRUE), heights = c(3, 1))

    # Main cross-network plot (no labels for cleaner visualization)
    plot(network,
         layout = layout,
         vertex.color = node_colors,
         vertex.size = node_sizes,
         vertex.frame.color = "white",
         vertex.frame.width = 1.5,
         vertex.label = NA,  # Remove all labels for cleaner plot
         edge.color = edge_colors,
         edge.width = edge_widths,
         edge.curved = 0.15,
         main = paste(group1_name, "vs", group2_name, "Cross-Network at", taxonomic_level, "Level"),
         sub = paste("Nodes:", vcount(network), "| Edges:", ecount(network),
                    "| Communities:", cross_network_result$community_count,
                    "| Hub nodes (top 5%):", sum(is_hub)),
         cex.main = 1.3,
         cex.sub = 0.9)

    # Add improved legend with muted colors (only positive correlations)
    legend("bottomright",
           legend = c(paste(group1_name, "nodes"), paste(group2_name, "nodes"),
                     "Positive correlation", "Hub nodes (top 5%)"),
           col = c(base_group1_color, base_group2_color, "#228B22", "black"),
           pch = c(16, 16, NA, 16),
           lty = c(NA, NA, 1, NA),
           cex = 0.9,
           bg = "white",
           box.lty = 1)

    # Reset layout
    layout(1)

    dev.off()

    cat("Cross-network plot saved:", filename, "\n")
    cat("Hub nodes identified:", sum(is_hub), "out of", vcount(network), "total nodes\n")
    cat("Group 1 (", cross_network_result$group1_name, ") nodes:", sum(V(network)$type == cross_network_result$group1_name), "\n")
    cat("Group 2 (", cross_network_result$group2_name, ") nodes:", sum(V(network)$type == cross_network_result$group2_name), "\n")

  }, error = function(e) {
    cat("Error creating cross-network plot:", e$message, "\n")
    cat("Error details:", e$message, "\n")
  })
}

# Generate all plots
# Plot individual networks
cat("\n--- PLOTTING INDIVIDUAL NETWORKS ---\n")
for (name in names(individual_networks)) {
  cat("Plotting", name, "\n")
  plot_individual_network(individual_networks[[name]], name, TAXONOMIC_LEVEL)
}

# Plot cross-correlation networks
cat("\n--- PLOTTING CROSS-CORRELATION NETWORKS ---\n")
for (name in names(cross_networks)) {
  cat("Plotting", name, "\n")
  # Extract group names from the network name
  parts <- strsplit(name, "_vs_")[[1]]
  group1_name <- parts[1]
  group2_name <- parts[2]
  plot_cross_network(cross_networks[[name]], group1_name, group2_name, TAXONOMIC_LEVEL)
}

cat("\n========== ANALYSIS COMPLETE ==========\n")
cat("All network analyses and plots have been generated!\n")
cat("Individual networks:", length(individual_networks), "\n")
cat("Cross-correlation networks:", length(cross_networks), "\n")
cat("Total PDF files created:", length(individual_networks) + length(cross_networks), "\n")
